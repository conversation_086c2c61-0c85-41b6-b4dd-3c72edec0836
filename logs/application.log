2025-07-30 18:07:00.073 [34mINFO [0;39m 25628 --- [background-preinit] o.h.validator.internal.util.Version : HV000001: Hibernate Validator 6.2.5.Final
2025-07-30 18:07:00.077 [34mINFO [0;39m 25628 --- [main] com.example.pure.PureApplication : Starting PureApplication using Java 17.0.13 on DESKTOP-DQ33ANO with PID 25628 (C:\MyHappy\Best\myapp\pure\target\classes started by Hao in C:\MyHappy\Best\myapp\pure)
2025-07-30 18:07:00.077 [39mDEBUG[0;39m 25628 --- [main] com.example.pure.PureApplication : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-07-30 18:07:00.078 [34mINFO [0;39m 25628 --- [main] com.example.pure.PureApplication : The following 1 profile is active: "dev"
2025-07-30 18:07:00.766 [31mWARN [0;39m 25628 --- [main] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.support.BeanDefinitionOverrideException: Invalid bean definition with name 'snowflakeIdGenerator' defined in class path resource [com/example/pure/config/SnowflakeConfig.class]: Cannot register bean definition [Root bean: class [null]; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=snowflakeConfig; factoryMethodName=snowflakeIdGenerator; initMethodName=null; destroyMethodName=(inferred); defined in class path resource [com/example/pure/config/SnowflakeConfig.class]] for bean 'snowflakeIdGenerator': There is already [Generic bean: class [com.example.pure.util.SnowflakeIdGenerator]; scope=singleton; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null; defined in file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\util\SnowflakeIdGenerator.class]] bound.
2025-07-30 18:07:00.774 [34mINFO [0;39m 25628 --- [main] o.s.b.a.l.ConditionEvaluationReportLoggingListener : 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-30 18:07:00.788 [1;31mERROR[0;39m 25628 --- [main] o.s.b.d.LoggingFailureAnalysisReporter : 

***************************
APPLICATION FAILED TO START
***************************

Description:

The bean 'snowflakeIdGenerator', defined in class path resource [com/example/pure/config/SnowflakeConfig.class], could not be registered. A bean with that name has already been defined in file [C:\MyHappy\Best\myapp\pure\target\classes\com\example\pure\util\SnowflakeIdGenerator.class] and overriding is disabled.

Action:

Consider renaming one of the beans or enabling overriding by setting spring.main.allow-bean-definition-overriding=true

