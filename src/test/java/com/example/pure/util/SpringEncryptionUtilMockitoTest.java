package com.example.pure.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

/**
 * SpringEncryptionUtil 方案2测试类
 * 使用 Mockito + 手动初始化，比SpringBootTest快，可测试依赖注入的方法
 * 适合测试需要Mock依赖的复杂业务逻辑
 */
@ExtendWith(MockitoExtension.class)
class SpringEncryptionUtilMockitoTest {

    @Mock
    private SnowflakeIdGenerator snowflakeIdGenerator;

    private SpringEncryptionUtil springEncryptionUtil;

    @BeforeEach
    void setUp() {
        springEncryptionUtil = new SpringEncryptionUtil(snowflakeIdGenerator);

        // 设置测试用的加密参数（Spring需要十六进制编码的盐值）
        ReflectionTestUtils.setField(springEncryptionUtil, "encryptionPassword", "testPassword123");
        ReflectionTestUtils.setField(springEncryptionUtil, "encryptionSalt", "74657374536c743435363738393031323334353637383930313233343536373839");

        // 模拟Snowflake ID生成（使用lenient避免不必要的stubbing警告）
        lenient().when(snowflakeIdGenerator.nextId()).thenReturn(123456789L);

        // 初始化加密工具
        springEncryptionUtil.init();
    }

    /**
     * 测试基础加密解密功能
     */
    @Test
    void testBasicEncryptDecrypt() {
        String originalText = "test-api-key-12345";
        
        // 测试加密
        String encrypted = springEncryptionUtil.encrypt(originalText);
        assertNotNull(encrypted);
        assertNotEquals(originalText, encrypted);
        
        // 测试解密
        String decrypted = springEncryptionUtil.decrypt(encrypted);
        assertEquals(originalText, decrypted);
    }

    /**
     * 测试Spring Crypto GCM加密解密（通过兼容密钥功能）
     */
    @Test
    void testSpringCryptoGCMEncryptDecrypt() {
        Long userId = 123L;
        String keyName = "test-encrypt";

        SpringEncryptionUtil.CompatibleKeyResult result =
            springEncryptionUtil.generateCompatibleApiKey(userId, keyName);

        assertNotNull(result.getCompatibleKey());
        assertTrue(result.getCompatibleKey().startsWith("sk-"));

        // 验证可以正确解析
        SpringEncryptionUtil.ParseResultV2 parseResult =
            springEncryptionUtil.parseCompatibleApiKey(result.getCompatibleKey());

        assertTrue(parseResult.isValid());
        assertEquals(userId, parseResult.getUserId());
        assertEquals(keyName, parseResult.getKeyName());
    }

    /**
     * 测试生成兼容密钥
     */
    @Test
    void testGenerateCompatibleApiKey() {
        Long userId = 123L;
        String keyName = "test-key";

        SpringEncryptionUtil.CompatibleKeyResult result =
            springEncryptionUtil.generateCompatibleApiKey(userId, keyName);

        assertNotNull(result);
        assertNotNull(result.getCompatibleKey());
        assertNotNull(result.getSecurityHash());
        assertEquals(123456789L, result.getSnowflakeId());

        // 验证密钥格式
        assertTrue(result.getCompatibleKey().startsWith("sk-"));
    }

    /**
     * 测试解析兼容密钥
     */
    @Test
    void testParseCompatibleApiKey() {
        Long userId = 456L;
        String keyName = "parse-test";

        // 生成密钥
        SpringEncryptionUtil.CompatibleKeyResult result =
            springEncryptionUtil.generateCompatibleApiKey(userId, keyName);

        // 解析密钥
        SpringEncryptionUtil.ParseResultV2 parseResult =
            springEncryptionUtil.parseCompatibleApiKey(result.getCompatibleKey());

        assertTrue(parseResult.isValid());
        assertEquals(userId, parseResult.getUserId());
        assertEquals(keyName, parseResult.getKeyName());
        assertNotNull(parseResult.getTimestamp());
        assertEquals("解析成功", parseResult.getMessage());
    }

    /**
     * 测试验证兼容密钥
     */
    @Test
    void testValidateCompatibleKey() {
        Long userId = 789L;
        String keyName = "validation-test";

        SpringEncryptionUtil.CompatibleKeyResult result =
            springEncryptionUtil.generateCompatibleApiKey(userId, keyName);

        boolean isValid = springEncryptionUtil.validateCompatibleKey(result.getCompatibleKey());
        assertTrue(isValid);
    }

    /**
     * 测试无效的密钥格式
     */
    @Test
    void testInvalidKeyFormats() {
        // 测试无效的密钥格式
        assertFalse(springEncryptionUtil.validateCompatibleKey(null));
        assertFalse(springEncryptionUtil.validateCompatibleKey(""));
        assertFalse(springEncryptionUtil.validateCompatibleKey("invalid-key"));
        assertFalse(springEncryptionUtil.validateCompatibleKey("sk-invalid"));
        
        // 测试解析无效密钥
        SpringEncryptionUtil.ParseResultV2 parseResult =
            springEncryptionUtil.parseCompatibleApiKey("sk-invalid");
        assertFalse(parseResult.isValid());
        assertNotNull(parseResult.getMessage());
    }

    /**
     * 测试加密的一致性（相同输入应产生不同输出，因为Spring Crypto使用随机IV）
     */
    @Test
    void testEncryptionConsistency() {
        Long userId = 999L;
        String keyName = "consistency-test";

        SpringEncryptionUtil.CompatibleKeyResult result1 =
            springEncryptionUtil.generateCompatibleApiKey(userId, keyName);
        SpringEncryptionUtil.CompatibleKeyResult result2 =
            springEncryptionUtil.generateCompatibleApiKey(userId, keyName);

        // 由于使用随机IV和时间戳，相同输入的加密结果应该不同
        assertNotEquals(result1.getCompatibleKey(), result2.getCompatibleKey());

        // 但都应该能正确解析出相同的用户ID和密钥名称
        SpringEncryptionUtil.ParseResultV2 parseResult1 =
            springEncryptionUtil.parseCompatibleApiKey(result1.getCompatibleKey());
        SpringEncryptionUtil.ParseResultV2 parseResult2 =
            springEncryptionUtil.parseCompatibleApiKey(result2.getCompatibleKey());

        assertEquals(userId, parseResult1.getUserId());
        assertEquals(userId, parseResult2.getUserId());
        assertEquals(keyName, parseResult1.getKeyName());
        assertEquals(keyName, parseResult2.getKeyName());
    }

    /**
     * 测试空密钥名称的处理
     */
    @Test
    void testKeyGenerationWithNullOrEmptyKeyName() {
        Long userId = 999L;
        
        SpringEncryptionUtil.CompatibleKeyResult result1 =
            springEncryptionUtil.generateCompatibleApiKey(userId, null);
        SpringEncryptionUtil.CompatibleKeyResult result2 =
            springEncryptionUtil.generateCompatibleApiKey(userId, "");
        SpringEncryptionUtil.CompatibleKeyResult result3 =
            springEncryptionUtil.generateCompatibleApiKey(userId, "   ");

        assertNotNull(result1.getCompatibleKey());
        assertNotNull(result2.getCompatibleKey());
        assertNotNull(result3.getCompatibleKey());

        // 解析并验证默认密钥名称
        SpringEncryptionUtil.ParseResultV2 parseResult1 =
            springEncryptionUtil.parseCompatibleApiKey(result1.getCompatibleKey());
        assertEquals("default", parseResult1.getKeyName());
    }

    /**
     * 测试无效用户ID的处理
     */
    @Test
    void testInvalidUserIdHandling() {
        assertThrows(RuntimeException.class, () -> {
            springEncryptionUtil.generateCompatibleApiKey(null, "test");
        });

        assertThrows(RuntimeException.class, () -> {
            springEncryptionUtil.generateCompatibleApiKey(0L, "test");
        });

        assertThrows(RuntimeException.class, () -> {
            springEncryptionUtil.generateCompatibleApiKey(-1L, "test");
        });
    }

    /**
     * 测试无效数据解密
     */
    @Test
    void testInvalidDataDecryption() {
        SpringEncryptionUtil.ParseResultV2 result1 =
            springEncryptionUtil.parseCompatibleApiKey("sk-invalid-data");
        assertFalse(result1.isValid());

        SpringEncryptionUtil.ParseResultV2 result2 =
            springEncryptionUtil.parseCompatibleApiKey("sk-dGVzdA"); // 有效base64但数据无效
        assertFalse(result2.isValid());
    }

    /**
     * 测试API密钥脱敏功能
     */
    @Test
    void testMaskApiKey() {
        // 测试OpenAI格式密钥脱敏
        String openaiKey = "sk-1234567890abcdefghijklmnopqrstuvwxyz";
        String masked = springEncryptionUtil.maskApiKey(openaiKey);
        assertEquals("sk-1234****wxyz", masked);
        
        // 测试普通格式密钥脱敏
        String normalKey = "abcdefghijklmnop";
        String maskedNormal = springEncryptionUtil.maskApiKey(normalKey);
        assertEquals("abcd****mnop", maskedNormal);
        
        // 测试短密钥
        String shortKey = "123";
        String maskedShort = springEncryptionUtil.maskApiKey(shortKey);
        assertEquals("****", maskedShort);
        
        // 测试null密钥
        String maskedNull = springEncryptionUtil.maskApiKey(null);
        assertEquals("****", maskedNull);
    }

    /**
     * 测试Mock Snowflake ID生成器的行为
     */
    @Test
    void testMockedSnowflakeIdGenerator() {
        // 设置特定的Mock行为
        when(snowflakeIdGenerator.nextId()).thenReturn(987654321L);
        
        Long userId = 123L;
        String keyName = "mock-test";
        
        SpringEncryptionUtil.CompatibleKeyResult result =
            springEncryptionUtil.generateCompatibleApiKey(userId, keyName);
        
        // 验证Mock的ID被正确使用
        assertEquals(987654321L, result.getSnowflakeId());
        assertNotNull(result.getCompatibleKey());
        assertNotNull(result.getSecurityHash());
    }

    /**
     * 测试加密解密的边界情况
     */
    @Test
    void testEncryptDecryptEdgeCases() {
        // 测试空字符串加密（应该抛出异常）
        assertThrows(RuntimeException.class, () -> {
            springEncryptionUtil.encrypt("");
        });
        
        assertThrows(RuntimeException.class, () -> {
            springEncryptionUtil.encrypt("   ");
        });
        
        assertThrows(RuntimeException.class, () -> {
            springEncryptionUtil.encrypt(null);
        });
        
        // 测试空字符串解密（应该抛出异常）
        assertThrows(RuntimeException.class, () -> {
            springEncryptionUtil.decrypt("");
        });
        
        assertThrows(RuntimeException.class, () -> {
            springEncryptionUtil.decrypt(null);
        });
        
        // 测试无效的加密字符串解密
        assertThrows(RuntimeException.class, () -> {
            springEncryptionUtil.decrypt("invalid-encrypted-string");
        });
    }
}
