package com.example.pure.util;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.security.crypto.encrypt.Encryptors;
import org.springframework.security.crypto.encrypt.TextEncryptor;

import javax.swing.*;
import java.lang.reflect.Field;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SpringEncryptionUtil 纯单元测试类（方案1）
 * 测试不依赖Spring容器的纯逻辑方法
 * 启动速度最快，适合测试加密解密等核心逻辑
 */
class SpringEncryptionUtilTest {

    private SpringEncryptionUtil springEncryptionUtil;
    private TextEncryptor textEncryptor;

    @BeforeEach
    void setUp() throws Exception {
        // 创建一个简单的SnowflakeIdGenerator实例（不使用Mock）
        SnowflakeIdGenerator snowflakeIdGenerator = new SnowflakeIdGenerator(1L, 1L);
        springEncryptionUtil = new SpringEncryptionUtil(snowflakeIdGenerator);

        // 使用反射设置私有字段（避免Spring依赖）
        setPrivateField(springEncryptionUtil, "encryptionPassword", "testPassword123");
        setPrivateField(springEncryptionUtil, "encryptionSalt", "74657374536c743435363738393031323334353637383930313233343536373839");

        // 手动创建TextEncryptor用于基础加密测试
        textEncryptor = Encryptors.text("testPassword123", "74657374536c743435363738393031323334353637383930313233343536373839");

        // 初始化加密工具
        springEncryptionUtil.init();
    }

    /**
     * 使用反射设置私有字段
     */
    private void setPrivateField(Object target, String fieldName, Object value) throws Exception {
        Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }

    /**
     * 测试基础的加密解密功能（纯逻辑测试）
     */
    @Test
    void testBasicEncryptDecrypt() {
        String originalText = "test-api-key-12345";

        // 测试加密
        String encrypted = springEncryptionUtil.encrypt(originalText);
        assertNotNull(encrypted);
        assertNotEquals(originalText, encrypted);

        // 测试解密
        String decrypted = springEncryptionUtil.decrypt(encrypted);
        assertEquals(originalText, decrypted);
    }



    /**
     * 测试API密钥脱敏功能
     */
    @Test
    void testMaskApiKey() {
        // 测试OpenAI格式密钥脱敏
        String openaiKey = "sk-1234567890abcdefghijklmnopqrstuvwxyz";
        String masked = springEncryptionUtil.maskApiKey(openaiKey);
        assertEquals("sk-1234****wxyz", masked);

        // 测试普通格式密钥脱敏
        String normalKey = "abcdefghijklmnop";
        String maskedNormal = springEncryptionUtil.maskApiKey(normalKey);
        assertEquals("abcd****mnop", maskedNormal);

        // 测试短密钥
        String shortKey = "123";
        String maskedShort = springEncryptionUtil.maskApiKey(shortKey);
        assertEquals("****", maskedShort);

        // 测试null密钥
        String maskedNull = springEncryptionUtil.maskApiKey(null);
        assertEquals("****", maskedNull);
    }

    /**
     * 测试加密解密的边界情况
     */
    @Test
    void testEncryptDecryptEdgeCases() {
        // 测试空字符串加密（应该抛出异常）
        assertThrows(RuntimeException.class, () -> {
            springEncryptionUtil.encrypt("");
        });

        assertThrows(RuntimeException.class, () -> {
            springEncryptionUtil.encrypt("   ");
        });

        assertThrows(RuntimeException.class, () -> {
            springEncryptionUtil.encrypt(null);
        });

        // 测试空字符串解密（应该抛出异常）
        assertThrows(RuntimeException.class, () -> {
            springEncryptionUtil.decrypt("");
        });

        assertThrows(RuntimeException.class, () -> {
            springEncryptionUtil.decrypt(null);
        });

        // 测试无效的加密字符串解密
        assertThrows(RuntimeException.class, () -> {
            springEncryptionUtil.decrypt("invalid-encrypted-string");
        });
    }

    /**
     * 测试加密一致性（相同输入多次加密应产生不同结果）
     */
    @Test
    void testEncryptionConsistency() {
        String originalText = "consistency-test-key";

        // 多次加密相同内容
        String encrypted1 = springEncryptionUtil.encrypt(originalText);
        String encrypted2 = springEncryptionUtil.encrypt(originalText);

        // 加密结果应该不同（因为使用了随机IV）
        assertNotEquals(encrypted1, encrypted2);

        // 但解密结果应该相同
        String decrypted1 = springEncryptionUtil.decrypt(encrypted1);
        String decrypted2 = springEncryptionUtil.decrypt(encrypted2);

        assertEquals(originalText, decrypted1);
        assertEquals(originalText, decrypted2);
        assertEquals(decrypted1, decrypted2);
    }

    /**
     * 测试兼容密钥生成和解析（需要依赖注入的方法）
     */
    @Test
    void testCompatibleKeyGeneration() {
        Long userId = 123L;
        String keyName = "test-key";

        SpringEncryptionUtil.CompatibleKeyResult result =
            springEncryptionUtil.generateCompatibleApiKey(userId, keyName);

        assertNotNull(result);
        assertNotNull(result.getCompatibleKey());
        assertNotNull(result.getSecurityHash());
        assertNotNull(result.getSnowflakeId());

        // 验证密钥格式
        assertTrue(result.getCompatibleKey().startsWith("sk-"));
        assertTrue(result.getCompatibleKey().length() > 10);
    }

    /**
     * 测试兼容密钥解析
     */
    @Test
    void testCompatibleKeyParsing() {
        Long userId = 456L;
        String keyName = "parse-test";

        // 生成密钥
        SpringEncryptionUtil.CompatibleKeyResult result =
            springEncryptionUtil.generateCompatibleApiKey(userId, keyName);

        // 解析密钥
        SpringEncryptionUtil.ParseResultV2 parseResult =
            springEncryptionUtil.parseCompatibleApiKey(result.getCompatibleKey());

        assertTrue(parseResult.isValid());
        assertEquals(userId, parseResult.getUserId());
        assertEquals(keyName, parseResult.getKeyName());
        assertNotNull(parseResult.getTimestamp());
        assertEquals("解析成功", parseResult.getMessage());
    }

    /**
     * 测试兼容密钥验证
     */
    @Test
    void testCompatibleKeyValidation() {
        Long userId = 789L;
        String keyName = "validation-test";

        SpringEncryptionUtil.CompatibleKeyResult result =
            springEncryptionUtil.generateCompatibleApiKey(userId, keyName);

        boolean isValid = springEncryptionUtil.validateCompatibleKey(result.getCompatibleKey());
        assertTrue(isValid);

        // 测试无效格式
        assertFalse(springEncryptionUtil.validateCompatibleKey(null));
        assertFalse(springEncryptionUtil.validateCompatibleKey(""));
        assertFalse(springEncryptionUtil.validateCompatibleKey("invalid-key"));
        assertFalse(springEncryptionUtil.validateCompatibleKey("sk-invalid"));
    }

    /**
     * 测试空密钥名称的处理
     */
    @Test
    void testKeyGenerationWithNullOrEmptyKeyName() {
        Long userId = 999L;

        SpringEncryptionUtil.CompatibleKeyResult result1 =
            springEncryptionUtil.generateCompatibleApiKey(userId, null);
        SpringEncryptionUtil.CompatibleKeyResult result2 =
            springEncryptionUtil.generateCompatibleApiKey(userId, "");
        SpringEncryptionUtil.CompatibleKeyResult result3 =
            springEncryptionUtil.generateCompatibleApiKey(userId, "   ");

        assertNotNull(result1.getCompatibleKey());
        assertNotNull(result2.getCompatibleKey());
        assertNotNull(result3.getCompatibleKey());

        // 解析并验证默认密钥名称
        SpringEncryptionUtil.ParseResultV2 parseResult1 =
            springEncryptionUtil.parseCompatibleApiKey(result1.getCompatibleKey());
        assertEquals("default", parseResult1.getKeyName());
    }

    /**
     * 测试无效用户ID的处理
     */
    @Test
    void testInvalidUserIdHandling() {
        assertThrows(RuntimeException.class, () -> {
            springEncryptionUtil.generateCompatibleApiKey(null, "test");
        });

        assertThrows(RuntimeException.class, () -> {
            springEncryptionUtil.generateCompatibleApiKey(0L, "test");
        });

        assertThrows(RuntimeException.class, () -> {
            springEncryptionUtil.generateCompatibleApiKey(-1L, "test");
        });
    }

    /**
     * 测试无效数据解析
     */
    @Test
    void testInvalidDataParsing() {
        // 测试无效密钥格式
        SpringEncryptionUtil.ParseResultV2 result1 =
            springEncryptionUtil.parseCompatibleApiKey("sk-invalid-data");
        assertFalse(result1.isValid());
        assertNotNull(result1.getMessage());

        SpringEncryptionUtil.ParseResultV2 result2 =
            springEncryptionUtil.parseCompatibleApiKey("sk-dGVzdA"); // 有效base64但数据无效
        assertFalse(result2.isValid());

        // 测试null和空字符串
        SpringEncryptionUtil.ParseResultV2 result3 =
            springEncryptionUtil.parseCompatibleApiKey(null);
        assertFalse(result3.isValid());

        SpringEncryptionUtil.ParseResultV2 result4 =
            springEncryptionUtil.parseCompatibleApiKey("invalid-format");
        assertFalse(result4.isValid());
    }

    /**
     * 测试长文本加密解密
     */
    @Test
    void testLongTextEncryption() {
        StringBuilder longText = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longText.append("这是一个很长的测试文本，用于测试加密解密功能的性能和正确性。");
        }

        String originalText = longText.toString();
        String encrypted = springEncryptionUtil.encrypt(originalText);
        String decrypted = springEncryptionUtil.decrypt(encrypted);

        assertEquals(originalText, decrypted);
        assertNotEquals(originalText, encrypted);
    }
}
