package com.example.pure.model.dto.request.openai;

import com.example.pure.model.entity.UserApiKey;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

/**
 * 添加API密钥请求DTO
 * <p>
 * 用于添加新的API密钥
 * </p>
 */
@Data
@Schema(description = "添加API密钥请求")
public class AddApiKeyRequest {

    /**
     * 提供商类型
     */
    @NotNull(message = "提供商类型不能为空")
    @Schema(description = "提供商类型", example = "OPENAI", required = true)
    private UserApiKey.ProviderType provider;

    /**
     * 密钥名称
     */
    @NotBlank(message = "密钥名称不能为空")
    @Schema(description = "密钥名称", example = "主要密钥", required = true)
    private String keyName;

    /**
     * API密钥
     */
    @NotBlank(message = "API密钥不能为空")
    @Schema(description = "API密钥", example = "sk-Apikey", required = true)
    private String apiKey;

    /**
     * 优先级
     */
    @Min(value = 1, message = "优先级不能小于1")
    @Max(value = 100, message = "优先级不能大于100")
    @Schema(description = "优先级（数字越小优先级越高）", example = "1")
    private Integer priority = 1;

    /**
     * 是否激活
     */
    @Schema(description = "是否激活", example = "true")
    private Boolean isActive = true;
}
